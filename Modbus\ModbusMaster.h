#ifndef MOBUS_MASTER_H
#define MOBUS_MASTER_H

#include "Crc_Caculator.h"
#include "ModbusConfig.h"


void InitModbus(UART_HandleTypeDef *phuart);

typedef struct _ModbusMaster{
	UART_HandleTypeDef* phuart;
	
	uint8_t DataModbusRTUSend[200];
	uint8_t BufferModbusRTURev[200];
	uint8_t ModbusRTUNumberByteRev;
	
}ModbusMaster;

extern ModbusMaster modbus_master;

#define FLAGS_MSG_MASTER_RTU_REV_IT 0x00000003U
#define FLAGS_MSG_MASTER_RTU_SEND_IT 0x00000003U

extern osEventFlagsId_t evt_rtu_master_rev_it_id;   
extern osEventFlagsId_t evt_rtu_master_send_ok_id;

uint8_t Config_Addr_Modul(uint16_t Addr_Setup);

void Wakeup_Slave(uint16_t Slave_Address);

ModbusMaster* ModbusMasterInit(UART_HandleTypeDef *phuart);

/********************************************************************
These exceptions are defined in the ModbusRTU protocol.  These can be
used by the slave to communicate problems with the transmission back
to the master who can also use these to easily check the exceptions.
The first exception is the only one that is not part of the protocol
specification.  The TIMEOUT exception is returned when no slave
responds to the master's request within the timeout period.
********************************************************************/
typedef enum _exception{ILLEGAL_FUNCTION=1,ILLEGAL_DATA_ADDRESS=2,
ILLEGAL_DATA_VALUE=3,SLAVE_DEVICE_FAILURE=4,ACKNOWLEDGE=5,SLAVE_DEVICE_BUSY=6,
MEMORY_PARITY_ERROR=8,GATEWAY_PATH_UNAVAILABLE=10,GATEWAY_TARGET_NO_RESPONSE=11,
TIMEOUT=12} exception;

/********************************************************************
These functions are defined in the ModbusRTU protocol.  These can be
used by the slave to check the incomming function.  See
ex_ModbusRTU_slave.c for example usage.
********************************************************************/
typedef enum _function{FUNC_READ_COILS=0x01,FUNC_READ_DISCRETE_INPUT=0x02,
FUNC_READ_HOLDING_REGISTERS=0x03,FUNC_READ_INPUT_REGISTERS=0x04,
FUNC_WRITE_SINGLE_COIL=0x05,FUNC_WRITE_SINGLE_REGISTER=0x06,
FUNC_READ_EXCEPTION_STATUS=0x07,FUNC_DIAGNOSTICS=0x08,
FUNC_GET_COMM_EVENT_COUNTER=0x0B,FUNC_GET_COMM_EVENT_LOG=0x0C,
FUNC_WRITE_MULTIPLE_COILS=0x0F,FUNC_WRITE_MULTIPLE_REGISTERS=0x10,
FUNC_REPORT_SLAVE_ID=0x11,FUNC_READ_FILE_RECORD=0x14,
FUNC_WRITE_FILE_RECORD=0x15,FUNC_MASK_WRITE_REGISTER=0x16,
FUNC_READ_WRITE_MULTIPLE_REGISTERS=0x17,FUNC_READ_FIFO_QUEUE=0x18} function;



void TransmitMessage(uint8_t Address,
	function Func, uint16_t StartAddress, uint16_t quantity,uint8_t* Data);


void MemCopy8bit(char NumberByte, uint8_t * BeginAddress, uint8_t * DestinateAddress);

void MemCopy16bit(char NumberByte, uint16_t * BeginAddress, uint16_t * DestinateAddress);

void ModbusRTU_EnableReceiver(void);

uint8_t ModbusRTU_check_respond(uint8_t slaveAddress, function Func);

void ModbusRTU_DisabelReceiver(void);

void ModbusRTU_read_coils(uint8_t address, uint16_t start_address, uint16_t quantity);

void ModbusRTU_read_discrete_input(uint8_t address, uint16_t start_address, uint16_t quantity);

void ModbusRTU_read_holding_registers(uint8_t address, uint16_t start_address, uint16_t quantity);

void ModbusRTU_read_input_registers(uint8_t address, uint16_t start_address, uint16_t quantity);

void ModbusRTU_write_single_coil(uint8_t address, uint16_t output_address, uint8_t on);

void ModbusRTU_write_single_register(uint8_t address, uint16_t reg_address, uint16_t reg_value);

void ModbusRTU_read_exception_status(uint8_t address);

void ModbusRTU_diagnostics(uint8_t address, uint16_t sub_func, uint16_t data);

void ModbusRTU_get_comm_event_counter(uint8_t address);

void ModbusRTU_get_comm_event_log(uint8_t address);

void ModbusRTU_write_multiple_coils(uint8_t address, uint16_t start_address, uint16_t quantity,
                           uint8_t *values);
                           
void ModbusRTU_write_multiple_registers(uint8_t address, uint16_t start_address, uint16_t quantity,
                           uint16_t *values);

void ModbusRTU_report_slave_id(uint8_t address);

void ModbusRTU_mask_write_register(uint8_t address, uint16_t reference_address,
                           uint16_t AND_mask, uint16_t OR_mask);

void ModbusRTU_read_write_multiple_registers(uint8_t address, uint16_t read_start,
                                    uint16_t read_quantity, uint16_t write_start,
                                    uint16_t write_quantity,
                                    uint16_t *write_registers_value);

void ModbusRTU_read_FIFO_queue(uint8_t address, uint16_t FIFO_address);

void ModbusRTU_serial_send_stop(void);

void ModbusRTU_serial_send_start(uint8_t to, uint8_t func);

void ModbusRTU_serial_putc(uint8_t c);

void ModbusRTU_REV_On(void);

void ModbusRTU_Send_On(void);

void InitModbusRTUBufferAddress(void);


#define bit_set(var,bit)   var |= (1<<bit)

#define bit_clear(var,bit)   var &= ~(1<<bit)

#define bit_test(var,bit) ((var &(1<<bit)) != 0)

#define make8(var,offset) ((var >> (offset*8)) & 0xff) 

#define make16(varhigh,varlow)  ((int16_t)(varhigh)<<8)+varlow

#endif
