# PWM Output trên chân PA11 - STM32F030

## Tổng quan
Code này cung cấp chức năng xuất PWM ra chân PA11 của STM32F030C8T6 sử dụng Timer 1 Channel 4.

## C<PERSON>u hình phần cứng
- **MCU**: STM32F030C8T6
- **Timer**: TIM1
- **Channel**: Channel 4
- **Pin**: PA11
- **Alternate Function**: AF2 (GPIO_AF2_TIM1)

## Thông số PWM
- **Frequency**: ~25 kHz (với System Clock 32MHz, Period = 1280)
- **Resolution**: 1280 steps (0-1280)
- **Duty Cycle Range**: 0% - 100%

### Tính toán tần số:
```
PWM Frequency = Timer Clock / (Prescaler + 1) / (Period + 1)
PWM Frequency = 32MHz / (0 + 1) / (1280 + 1) ≈ 25 kHz
```

## API Functions

### `void PWM_Start_PA11(void)`
Khởi động PWM trên chân PA11.

**Sử dụng:**
```c
PWM_Start_PA11();
```

### `void PWM_Stop_PA11(void)`
Dừng PWM trên chân PA11.

**Sử dụng:**
```c
PWM_Stop_PA11();
```

### `void PWM_SetDutyCycle_PA11(uint8_t duty_percent)`
Thiết lập duty cycle cho PWM.

**Tham số:**
- `duty_percent`: Phần trăm duty cycle (0-100)

**Sử dụng:**
```c
PWM_SetDutyCycle_PA11(50);  // 50% duty cycle
PWM_SetDutyCycle_PA11(75);  // 75% duty cycle
PWM_SetDutyCycle_PA11(0);   // 0% duty cycle (tắt)
PWM_SetDutyCycle_PA11(100); // 100% duty cycle (luôn bật)
```

## Ví dụ sử dụng

### Ví dụ 1: PWM cố định
```c
int main(void)
{
    // Khởi tạo hệ thống...
    
    // Bật PWM với duty cycle 50%
    PWM_Start_PA11();
    PWM_SetDutyCycle_PA11(50);
    
    while(1)
    {
        // Code khác...
    }
}
```

### Ví dụ 2: PWM thay đổi (như trong code demo)
```c
int main(void)
{
    // Khởi tạo hệ thống...
    
    PWM_Start_PA11();
    
    uint8_t duty = 0;
    uint8_t direction = 1;
    
    while(1)
    {
        PWM_SetDutyCycle_PA11(duty);
        
        if (direction) {
            duty += 5;
            if (duty >= 100) {
                duty = 100;
                direction = 0;
            }
        } else {
            duty -= 5;
            if (duty <= 0) {
                duty = 0;
                direction = 1;
            }
        }
        
        HAL_Delay(100);
    }
}
```

## Ứng dụng thực tế
- Điều khiển độ sáng LED
- Điều khiển tốc độ motor DC
- Tạo tín hiệu analog (với bộ lọc thông thấp)
- Điều khiển servo motor
- Điều khiển quạt (fan control)

## Lưu ý
1. Chân PA11 phải được cấu hình đúng trong STM32CubeMX
2. Timer 1 phải được khởi tạo với mode PWM
3. Đảm bảo clock cho Timer 1 đã được enable
4. Có thể thay đổi tần số PWM bằng cách điều chỉnh Prescaler và Period trong STM32CubeMX

## Troubleshooting
- **Không có tín hiệu PWM**: Kiểm tra cấu hình pin và timer
- **Tần số không đúng**: Kiểm tra System Clock và cấu hình Timer
- **Duty cycle không chính xác**: Kiểm tra giá trị Period trong timer config
