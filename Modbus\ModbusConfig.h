#ifndef MODBUS_CONFIG_H_
#define MODBUS_CONFIG_H_

#include "stm32f1xx_hal.h"
#include "main.h"
#include <stdbool.h>
#include "cmsis_os2.h"   

#ifndef MODBUS_MASTER
	#define MODBUS_MASTER 1
	
	#define USE_IDLE_LINE_MASTER 1
#endif

//extern UART_HandleTypeDef huart1;
//#define huartModbusRTU huart1


#define MAXBYTE_MASTER_RECEIVER 200

#ifdef SERIAL_USE_ENABLE_PIN
	#define ENABLE_RECEIVER  HAL_GPIO_WritePin(RS485_EN_GPIO_Port,RS485_EN_Pin,GPIO_PIN_RESET)
	#define DISABLE_RECEIVER HAL_GPIO_WritePin(RS485_EN_GPIO_Port,RS485_EN_Pin,GPIO_PIN_SET)
#endif

#endif
