#ifndef MODBUS_SLAVE_H_
#define MODBUS_SLAVE_H_



#ifdef MODBUS_SLAVE

#include <stdint.h>
#include "CallbackUART.h"
#include "Crc_Caculator.h"
#include "main.h"
#include "cmsis_os2.h"   
#include "ModbusConfig.h"

#define FLAGS_MSG_SLAVE_RTU_REV_IT 0x00000003U
#define FLAGS_MSG_SLAVE_RTU_REV 0x00000002U
#define FLAGS_MSG_SLAVE_RTU_COMPLETE 0x00000001U
extern osEventFlagsId_t evt_rtu_slave_rev_it_id;   
extern osEventFlagsId_t evt_rtu_slave_rev_id;  
extern osEventFlagsId_t evt_rtu_slave_complete_id; 

#define RTU_SLAVE_SERIAL_RX_BUFFER_SIZE  200      //size of send/rcv buffer
#define MAXBYTE_CONTINOUS_RECEIVER 200
#ifndef MOBUS_FUNCTION
#define MOBUS_FUNCTION
/********************************************************************
These exceptions are defined in the RTU_Slave protocol.  These can be
used by the slave to communicate problems with the transmission back
to the master who can also use these to easily check the exceptions.
The first exception is the only one that is not part of the protocol
specification.  The TIMEOUT exception is returned when no slave
responds to the master's request within the timeout period.
********************************************************************/

typedef enum _exception{ILLEGAL_FUNCTION=1,ILLEGAL_DATA_ADDRESS=2,
ILLEGAL_DATA_VALUE=3,SLAVE_DEVICE_FAILURE=4,ACKNOWLEDGE=5,SLAVE_DEVICE_BUSY=6,
MEMORY_PARITY_ERROR=8,GATEWAY_PATH_UNAVAILABLE=10,GATEWAY_TARGET_NO_RESPONSE=11,
TIMEOUT=12} exception;

/********************************************************************
These functions are defined in the RTU_Slave protocol.  These can be
used by the slave to check the incomming function.  See
ex_RTU_Slave_slave.c for example usage.
********************************************************************/

typedef enum _function{FUNC_READ_COILS=0x01,FUNC_READ_DISCRETE_INPUT=0x02,
FUNC_READ_HOLDING_REGISTERS=0x03,FUNC_READ_INPUT_REGISTERS=0x04,
FUNC_WRITE_SINGLE_COIL=0x05,FUNC_WRITE_SINGLE_REGISTER=0x06,
FUNC_READ_EXCEPTION_STATUS=0x07,FUNC_DIAGNOSTICS=0x08,
FUNC_GET_COMM_EVENT_COUNTER=0x0B,FUNC_GET_COMM_EVENT_LOG=0x0C,
FUNC_WRITE_MULTIPLE_COILS=0x0F,FUNC_WRITE_MULTIPLE_REGISTERS=0x10,
FUNC_REPORT_SLAVE_ID=0x11,FUNC_READ_FILE_RECORD=0x14,
FUNC_WRITE_FILE_RECORD=0x15,FUNC_MASK_WRITE_REGISTER=0x16,
FUNC_READ_WRITE_MULTIPLE_REGISTERS=0x17,FUNC_READ_FIFO_QUEUE=0x18} function;


#endif


typedef struct _RTU_Slave_read_sub_request_rsp
{
   uint8_t record_length;
   uint8_t reference_type;
   uint16_t data[((RTU_SLAVE_SERIAL_RX_BUFFER_SIZE)/2)-3];
} RTU_Slave_read_sub_request_rsp;

typedef struct _RTU_Slave_write_sub_request_rsp
{
   uint8_t reference_type;
   uint16_t file_number;
   uint16_t record_number;
   uint16_t record_length;
   uint16_t data[((RTU_SLAVE_SERIAL_RX_BUFFER_SIZE)/2)-8];
} RTU_Slave_write_sub_request_rsp;


typedef struct _RTU_SlaveData
{
	uint8_t SlaveAddress;
	uint8_t Message;// imfor message of master
	uint16_t start_address;// imfor message of master
	uint16_t *RegisterData;// imfor message of master
	uint8_t *CoidData;// imfor message of master
	uint16_t quantity;// quantity of coil or of register
	
	uint8_t Send_On_Flag;
	uint16_t Error_Count_Complete;
	
	exception SlaveState;
	
	uint8_t RevCompleteEvent;
	uint8_t RespondCompleteEvent;
	
	uint8_t LengthOfSendMessage;
	uint8_t LengthOfReceiveMessage;
	
} RTU_SlaveData;

void GetRTUInputRegister ( char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs );
void PutRTUInputRegister(  char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs );
void GetRTUHoldingRegister( char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs );
void PutRTUHoldingRegister( char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs );

void ModbusRTU_SlaveInit(uint8_t SlaveAdd);

void ModbusRTU_SlaveTask(void);

void ModbusRTU_Slave_Enable_Receiver(void);

uint8_t ModbusRTU_Slave_Wait_Message(void);

uint8_t ModbusRTU_Slave_CheckMessage(void);

void ModbusRTU_SlaveProcessing(RTU_SlaveData* SlaveData );

void RTU_Slave_calc_crc(char data);

void RTU_Slave_exception_rsp(uint8_t address, uint16_t func, exception error);


void RTU_Slave_read_coils_rsp(uint8_t address, uint8_t byte_count, uint8_t* coil_data);

void RTU_Slave_read_discrete_input_rsp(uint8_t address, uint8_t byte_count,
                                    uint8_t *input_data);

void RTU_Slave_read_holding_registers_rsp(uint8_t address, uint8_t byte_count,
                                        uint16_t *reg_data);

void RTU_Slave_read_input_registers_rsp(uint8_t address, uint8_t byte_count,
                                        uint16_t *input_data);

void RTU_Slave_write_single_coil_rsp(uint8_t address, uint16_t output_address,
                                    uint16_t output_value);

void RTU_Slave_write_single_register_rsp(uint8_t address, uint16_t reg_address,
                                        uint16_t reg_value);

void RTU_Slave_read_exception_status_rsp(uint8_t address, uint8_t data);

void RTU_Slave_diagnostics_rsp(uint8_t address, uint16_t sub_func, uint16_t data);

void RTU_Slave_get_comm_event_counter_rsp(uint8_t address, uint16_t status,
                                        uint16_t event_count);

void RTU_Slave_get_comm_event_log_rsp(uint8_t address, uint16_t status,
                                    uint16_t event_count, uint16_t message_count,
                                    uint8_t *events, uint8_t events_len);

void RTU_Slave_write_multiple_coils_rsp(uint8_t address, uint16_t start_address,
                                        uint16_t quantity);


void RTU_Slave_write_multiple_registers_rsp(uint8_t address, uint16_t start_address,
                                            uint16_t quantity);

void RTU_Slave_report_slave_id_rsp(uint8_t address, uint8_t slave_id, uint8_t run_status,
                              uint8_t *data, uint8_t data_len);

void RTU_Slave_read_file_record_rsp(uint8_t address, uint8_t byte_count,
                                    RTU_Slave_read_sub_request_rsp *request);

void RTU_Slave_write_file_record_rsp(uint8_t address, uint8_t byte_count,
                                    RTU_Slave_write_sub_request_rsp *request);

void RTU_Slave_mask_write_register_rsp(uint8_t address, uint16_t reference_address,
                           uint16_t AND_mask, uint16_t OR_mask);

void RTU_Slave_read_write_multiple_registers_rsp(uint8_t address, uint8_t data_len,
                                                uint16_t *data);

void RTU_Slave_read_FIFO_queue_rsp(uint8_t address, uint16_t FIFO_len, uint16_t *data);

void RTU_Slave_serial_send_stop(void);

void RTU_Slave_serial_send_start(uint8_t to, uint8_t func);

void RTU_Slave_serial_putc(uint8_t c);

void RTU_Slave_REV_On(void);

void RTU_Slave_Send_On(void);

void InitRTU_SlaveBufferAddress(void);

#define bit_set(var,bit)   var |= (1<<bit)

#define bit_clear(var,bit)   var &= ~(1<<bit)

#define bit_test(var,bit) ((var &(1<<bit)) != 0)

#define make8(var,offset) ((var >> (offset*8)) & 0xff) 

#define make16(varhigh,varlow)  ((int16_t)(varhigh)<<8)+varlow

#define swape16(var) ((var >> 8) | (var<<8)) 

#endif

#endif
