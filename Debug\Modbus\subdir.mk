################################################################################
# Automatically-generated file. Do not edit!
# Toolchain: GNU Tools for STM32 (13.3.rel1)
################################################################################

# Add inputs and outputs from these tool invocations to the build variables 
C_SRCS += \
../Modbus/Crc_Caculator.c \
../Modbus/ModbusMaster.c \
../Modbus/ModbusSlave.c 

OBJS += \
./Modbus/Crc_Caculator.o \
./Modbus/ModbusMaster.o \
./Modbus/ModbusSlave.o 

C_DEPS += \
./Modbus/Crc_Caculator.d \
./Modbus/ModbusMaster.d \
./Modbus/ModbusSlave.d 


# Each subdirectory must supply rules for building sources it contributes
Modbus/%.o Modbus/%.su Modbus/%.cyclo: ../Modbus/%.c Modbus/subdir.mk
	arm-none-eabi-gcc "$<" -mcpu=cortex-m0 -std=gnu11 -g3 -DDEBUG -DUSE_HAL_DRIVER -DSTM32F030x8 -c -I../Core/Inc -I../Drivers/STM32F0xx_HAL_Driver/Inc -I../Drivers/STM32F0xx_HAL_Driver/Inc/Legacy -I../Drivers/CMSIS/Device/ST/STM32F0xx/Include -I../Drivers/CMSIS/Include -I"D:/Documents_Git/Documents/Zalo Received Files/FanControlF030 (1)/Modbus" -O0 -ffunction-sections -fdata-sections -Wall -fstack-usage -fcyclomatic-complexity -MMD -MP -MF"$(@:%.o=%.d)" -MT"$@" --specs=nano.specs -mfloat-abi=soft -mthumb -o "$@"

clean: clean-Modbus

clean-Modbus:
	-$(RM) ./Modbus/Crc_Caculator.cyclo ./Modbus/Crc_Caculator.d ./Modbus/Crc_Caculator.o ./Modbus/Crc_Caculator.su ./Modbus/ModbusMaster.cyclo ./Modbus/ModbusMaster.d ./Modbus/ModbusMaster.o ./Modbus/ModbusMaster.su ./Modbus/ModbusSlave.cyclo ./Modbus/ModbusSlave.d ./Modbus/ModbusSlave.o ./Modbus/ModbusSlave.su

.PHONY: clean-Modbus

