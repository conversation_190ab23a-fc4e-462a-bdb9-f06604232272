# PWM 100Hz trên chân PA11 - STM32F030

## Tổng quan
Code này tạo tín hiệu PWM với tần số chính xác 100Hz trên chân PA11 của STM32F030C8T6.

## Thông số kỹ thuật
- **Tần số PWM**: 100 Hz
- **Resolution**: 10.000 steps (0-9999) - <PERSON><PERSON> phân giải cực cao
- **Duty Cycle Range**: 0% - 100%
- **Pin**: PA11 (TIM1_CH4)
- **Timer**: TIM1
- **Prescaler**: 31 (chia 32)
- **Period**: 9999

## Tính toán tần số
```
Timer Clock = 32MHz
Prescaler = 31 (chia cho 32)
Period = 9999 (10000 steps)

PWM Frequency = 32,000,000 / (32 × 10000) = 100 Hz
```

## Cấu hình phần cứng
- **MCU**: STM32F030C8T6
- **System Clock**: 32MHz
- **Timer Clock**: 32MHz / 32 = 1MHz
- **GPIO**: PA11 configured as AF2 (TIM1_CH4)

## Ưu điểm của PWM 100Hz

### 🎯 **Resolution cực cao**
- **10.000 steps**: Độ chính xác 0.01% cho duty cycle
- **Điều khiển mịn**: Thích hợp cho servo motor, điều khiển chính xác

### 📊 **Ví dụ về độ chính xác:**
```
1% duty cycle = 100 steps
0.5% duty cycle = 50 steps  
0.1% duty cycle = 10 steps
0.01% duty cycle = 1 step
```

### 🔧 **Ứng dụng phù hợp:**
- **Servo motor**: Tần số chuẩn 50-100Hz
- **Điều khiển vị trí**: Cần độ chính xác cao
- **LED dimming**: Điều khiển độ sáng mịn
- **Motor stepper**: Điều khiển tốc độ chính xác

## API Functions

### `uint32_t PWM_Setup_100Hz_PA11(void)`
Cấu hình timer để tạo PWM 100Hz với resolution cao.

**Trả về:**
- Tần số thực tế (100 Hz)

**Sử dụng:**
```c
uint32_t freq = PWM_Setup_100Hz_PA11();  // freq = 100
```

### `void PWM_Start_PA11(void)`
Khởi động PWM trên chân PA11.

### `void PWM_Stop_PA11(void)`
Dừng PWM trên chân PA11.

### `void PWM_SetDutyCycle_PA11(uint8_t duty_percent)`
Thiết lập duty cycle cho PWM.

**Tham số:**
- `duty_percent`: Phần trăm duty cycle (0-100)

**Ví dụ với resolution cao:**
```c
PWM_SetDutyCycle_PA11(50);   // 50.00% = 5000/10000
PWM_SetDutyCycle_PA11(25);   // 25.00% = 2500/10000
PWM_SetDutyCycle_PA11(75);   // 75.00% = 7500/10000
```

## Ví dụ sử dụng

### Ví dụ 1: Servo motor control
```c
void Servo_SetAngle(float angle_degrees)
{
    // Servo: 1ms = 0°, 1.5ms = 90°, 2ms = 180°
    // Với chu kỳ 10ms (100Hz)
    
    if (angle_degrees < 0) angle_degrees = 0;
    if (angle_degrees > 180) angle_degrees = 180;
    
    // Tính duty cycle: 1ms-2ms trong chu kỳ 10ms = 10%-20%
    float duty_percent = 10.0 + (angle_degrees / 180.0) * 10.0;
    
    PWM_SetDutyCycle_PA11((uint8_t)duty_percent);
}

int main(void)
{
    // Khởi tạo hệ thống...
    PWM_Setup_100Hz_PA11();
    PWM_Start_PA11();
    
    // Quét servo từ 0° đến 180°
    for (float angle = 0; angle <= 180; angle += 1.0) {
        Servo_SetAngle(angle);
        HAL_Delay(50);  // 50ms mỗi bước
    }
}
```

### Ví dụ 2: LED dimming mịn
```c
int main(void)
{
    PWM_Setup_100Hz_PA11();
    PWM_Start_PA11();
    
    // Fade in/out với độ mịn cao
    for (uint8_t brightness = 0; brightness <= 100; brightness++) {
        PWM_SetDutyCycle_PA11(brightness);
        HAL_Delay(20);  // Fade mịn trong 2 giây
    }
    
    for (uint8_t brightness = 100; brightness > 0; brightness--) {
        PWM_SetDutyCycle_PA11(brightness);
        HAL_Delay(20);  // Fade mịn trong 2 giây
    }
}
```

### Ví dụ 3: Motor speed control chính xác
```c
void Motor_SetSpeed_Precise(float speed_percent)
{
    // Speed từ 0.00% đến 100.00%
    if (speed_percent < 0) speed_percent = 0;
    if (speed_percent > 100) speed_percent = 100;
    
    PWM_SetDutyCycle_PA11((uint8_t)speed_percent);
}

int main(void)
{
    PWM_Setup_100Hz_PA11();
    PWM_Start_PA11();
    
    // Tăng tốc từ 0% đến 100% với bước 0.5%
    for (float speed = 0; speed <= 100; speed += 0.5) {
        Motor_SetSpeed_Precise(speed);
        HAL_Delay(100);
    }
}
```

## So sánh với các tần số khác

| Tần số | Resolution | Ứng dụng | Ưu điểm | Nhược điểm |
|--------|------------|----------|---------|------------|
| 100Hz | 10,000 steps | Servo, chính xác | Resolution cực cao | Tần số thấp |
| 1kHz | 1,000 steps | Motor DC | Cân bằng tốt | Trung bình |
| 20kHz | 1,600 steps | Switching PSU | Ngoài tầm nghe | Resolution thấp hơn |

## Chu kỳ và timing
```
Tần số: 100Hz
Chu kỳ: 10ms
1% duty cycle = 0.1ms ON, 9.9ms OFF
50% duty cycle = 5ms ON, 5ms OFF
90% duty cycle = 9ms ON, 1ms OFF
```

## Lưu ý quan trọng
1. **Tần số thấp**: Có thể thấy nhấp nháy LED ở duty cycle thấp
2. **Resolution cao**: Cho phép điều khiển rất chính xác
3. **Servo motor**: Tần số 100Hz phù hợp cho servo chuẩn
4. **Timing critical**: Thích hợp cho ứng dụng cần timing chính xác

## Troubleshooting
- **LED nhấp nháy**: Bình thường với 100Hz, tăng tần số nếu cần
- **Servo không hoạt động**: Kiểm tra duty cycle 10-20% cho servo
- **Tần số không đúng**: Kiểm tra System Clock = 32MHz
- **Resolution không đủ**: 100Hz đã cho resolution tối đa với STM32F030
