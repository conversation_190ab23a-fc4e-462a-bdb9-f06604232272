/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.h
  * @brief          : Header for main.c file.
  *                   This file contains the common defines of the application.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
#include "stm32f0xx_hal.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */

/* USER CODE END Includes */

/* Exported types ------------------------------------------------------------*/
/* USER CODE BEGIN ET */

/* USER CODE END ET */

/* Exported constants --------------------------------------------------------*/
/* USER CODE BEGIN EC */

/* USER CODE END EC */

/* Exported macro ------------------------------------------------------------*/
/* USER CODE BEGIN EM */

/* USER CODE END EM */

/* Exported functions prototypes ---------------------------------------------*/
void Error_Handler(void);

/* USER CODE BEGIN EFP */

// PWM Control Functions for PA11 (TIM1_CH4) - 100Hz
void PWM_SetDutyCycle_PA11(uint8_t duty_percent);
void PWM_Start_PA11(void);
void PWM_Stop_PA11(void);
uint32_t PWM_Setup_100Hz_PA11(void);

/* USER CODE END EFP */

/* Private defines -----------------------------------------------------------*/
#define LED1_Pin GPIO_PIN_13
#define LED1_GPIO_Port GPIOC
#define ADC_OUT_Pin GPIO_PIN_0
#define ADC_OUT_GPIO_Port GPIOA
#define LED_485_Pin GPIO_PIN_4
#define LED_485_GPIO_Port GPIOA
#define IN_1_Pin GPIO_PIN_5
#define IN_1_GPIO_Port GPIOA
#define LED_F4_Pin GPIO_PIN_12
#define LED_F4_GPIO_Port GPIOB
#define LED_F3_Pin GPIO_PIN_13
#define LED_F3_GPIO_Port GPIOB
#define LED_F2_Pin GPIO_PIN_14
#define LED_F2_GPIO_Port GPIOB
#define LED_F1_Pin GPIO_PIN_15
#define LED_F1_GPIO_Port GPIOB
#define USART1_EN_Pin GPIO_PIN_5
#define USART1_EN_GPIO_Port GPIOB

/* USER CODE BEGIN Private defines */

/* USER CODE END Private defines */

#ifdef __cplusplus
}
#endif

#endif /* __MAIN_H */
