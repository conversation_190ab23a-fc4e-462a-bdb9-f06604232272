
#ifdef MODBUS_MASTER

#include "ModbusMaster.h"
#include "Crc_Caculator.h"
#include "UartCallback.h"

ModbusMaster modbus_master;


osEventFlagsId_t evt_rtu_master_rev_it_id;
osEventFlagsId_t evt_rtu_master_send_ok_id;

#define MODBUS_MEMORY
//-------------------------------------------ModbusRTU define -------------------



extern union
{
   uint8_t b[2];
   uint16_t d;
} serial_crc;


void InitModbus(UART_HandleTypeDef *phuart)
{
	ModbusMasterInit(phuart);

	evt_rtu_master_rev_it_id = osEventFlagsNew(NULL);
	if (evt_rtu_master_rev_it_id == NULL) {
    // Event Flags object not created, handle failure
	}
	evt_rtu_master_send_ok_id= osEventFlagsNew(NULL);
	// add task communicate
}

//-----------------------------------------Microchip UART program ---------------
#define ERROR_STATE 0
#define REV_STATE 1

ModbusMaster* ModbusMasterInit(UART_HandleTypeDef *phuart)
{
		modbus_master.phuart=phuart;
		modbus_master.ModbusRTUNumberByteRev=0;
			
		for(uint8_t i=0;i<200;i++) modbus_master.BufferModbusRTURev[i]=0;
	
		#ifdef SERIAL_USE_ENABLE_PIN
		    ENABLE_RECEIVER;
		#endif
		return &modbus_master;
}



//----------------------------------------------------------------------------
// ---------------------------Put to the buffer -----------------------------
//---------------------------------------------------------------------------
unsigned char CountModbusRTU=0;

void ModbusRTU_serial_putc(uint8_t c)
{
   modbus_master.DataModbusRTUSend[CountModbusRTU]=c;
      CountModbusRTU++;
   calc_crc(c);
}

// Purpose:    Send a message over the RS485 bus
// Inputs:     1) The destination address
//             2) The number of bytes of data to send
//             3) A pointer to the data to send
//             4) The length of the data
// Outputs:    TRUE if successful
//             FALSE if failed
// Note:       Format:  source | destination | data-length | data | checksum
void ModbusRTU_serial_send_start(uint8_t to, uint8_t func)
{
    serial_crc.d=0xFFFF;
    CountModbusRTU=0;

   ModbusRTU_serial_putc(to);
   ModbusRTU_serial_putc(func);
}

// Purpose:    Ends a message over the RS485 Bus
// Inputs:     Character
// Outputs:    None
void ModbusRTU_serial_send_stop(void)
{
   uint8_t crc_low, crc_high;

   crc_high=serial_crc.b[1];
   crc_low=serial_crc.b[0];

   ModbusRTU_serial_putc(crc_high);
   ModbusRTU_serial_putc(crc_low);
}

void ModbusRTU_Send_On(void)// send data
{
#ifdef SERIAL_USE_ENABLE_PIN// enalble Tranmiter
   DISABLE_RECEIVER;
	
#endif

	if(HAL_UART_Transmit_DMA(modbus_master.phuart, modbus_master.DataModbusRTUSend, CountModbusRTU)!=HAL_OK)
	{
		#ifdef SERIAL_USE_ENABLE_PIN// enalble Tranmiter
			 ENABLE_RECEIVER;
		#endif
	}
}

//- -------------------------------------- function ------------------------------

//////////////////////////////////////////////////////////////////////////////////////////
//// Master API                                                                       ////
//////////////////////////////////////////////////////////////////////////////////////////
void TransmitMessage(uint8_t Address,
	function Func, uint16_t StartAddress, uint16_t quantity,uint8_t* Data)
{
	switch(Func)
	{
		case FUNC_READ_HOLDING_REGISTERS:
			ModbusRTU_read_holding_registers( Address,  StartAddress, quantity);
	
		break;
		case FUNC_WRITE_SINGLE_REGISTER:
			
			ModbusRTU_write_single_register(Address, StartAddress, ((uint16_t*)Data)[0]);
		
		break;
		case FUNC_WRITE_MULTIPLE_REGISTERS:
			
			ModbusRTU_write_multiple_registers(Address, StartAddress, quantity,(uint16_t*)Data);
		
		break;		
		case FUNC_READ_INPUT_REGISTERS:
			
			ModbusRTU_read_input_registers(Address, StartAddress, quantity);
		
		break;
		case FUNC_READ_DISCRETE_INPUT:
			
			ModbusRTU_read_discrete_input(Address, StartAddress, quantity);
		
		break;		
		default:
			;
		break;
	}
}


/********************************************************************
The following functions are defined in the ModbusRTU protocol.  Please
refer to http://www.ModbusRTU.org for the purpose of each of these.
All functions take the slaves address as their first parameter.
Each function returns the exception code received from the response.
The function will return 0 if there were no errors in transmission.
********************************************************************/


void ModbusRTU_EnableReceiver(void)
{
	 if(modbus_master.phuart->hdmarx->State== HAL_DMA_STATE_READY)
	 {
			if(HAL_UARTEx_ReceiveToIdle_DMA(modbus_master.phuart, modbus_master.BufferModbusRTURev, MAXBYTE_MASTER_RECEIVER) == HAL_OK){
					__HAL_DMA_DISABLE_IT( modbus_master.phuart->hdmarx, DMA_IT_HT);
			}
			else
			{
				HAL_UART_AbortReceive_IT(modbus_master.phuart);
			}		
	 }
}

void ModbusRTU_DisabelReceiver(void)
{
	HAL_UART_AbortReceive_IT(modbus_master.phuart);
	modbus_master.ModbusRTUNumberByteRev=0;
	
}

uint32_t FlagsRev;

uint8_t ModbusRTU_check_respond(uint8_t slaveAddress, function Func)
{

	FlagsRev = osEventFlagsWait(evt_rtu_master_rev_it_id, FLAGS_MSG_MASTER_RTU_REV_IT, osFlagsWaitAny, 500);
		osEventFlagsClear(evt_rtu_master_rev_it_id,FlagsRev);
		if((FlagsRev & FLAGS_MSG_MASTER_RTU_REV_IT) == FLAGS_MSG_MASTER_RTU_REV_IT){// 100ms
             if(CheckFarm(modbus_master.BufferModbusRTURev,modbus_master.ModbusRTUNumberByteRev)==1) 
             {
               if(modbus_master.BufferModbusRTURev[0]== slaveAddress &&  modbus_master.BufferModbusRTURev[1]== Func )
               {
                  //*Excep = ACKNOWLEDGE;// Ok 
								  return 1;	
               }
               else
               {
                  //*Excep = SLAVE_DEVICE_FAILURE;
								 return 2;	
               }
             }else
             {
               //*Excep = SLAVE_DEVICE_FAILURE;
							  return 2;	
             }	
				}
			
		// =============
         return 0;
}

/*
read_coils
Input:     int8       address            Slave Address
           int16      start_address      Address to start reading from
           int16      quantity           Amount of addresses to read
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_read_coils(uint8_t address, uint16_t start_address, uint16_t quantity)
{
   ModbusRTU_serial_send_start(address, FUNC_READ_COILS);

   ModbusRTU_serial_putc(make8(start_address,1));
   ModbusRTU_serial_putc(make8(start_address,0));

   ModbusRTU_serial_putc(make8(quantity,1));
   ModbusRTU_serial_putc(make8(quantity,0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();
}

/*
read_discrete_input
Input:     int8       address            Slave Address
           int16      start_address      Address to start reading from
           int16      quantity           Amount of addresses to read
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_read_discrete_input(uint8_t address, uint16_t start_address, uint16_t quantity)
{
   ModbusRTU_serial_send_start(address, FUNC_READ_DISCRETE_INPUT);

   ModbusRTU_serial_putc(make8(start_address,1));
   ModbusRTU_serial_putc(make8(start_address,0));

   ModbusRTU_serial_putc(make8(quantity,1));
   ModbusRTU_serial_putc(make8(quantity,0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();
}

/*
read_holding_registers
Input:     int8       address            Slave Address
           int16      start_address      Address to start reading from
           int16      quantity           Amount of addresses to read
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_read_holding_registers(uint8_t address, uint16_t start_address, uint16_t quantity)
{
   ModbusRTU_serial_send_start(address, FUNC_READ_HOLDING_REGISTERS);

   ModbusRTU_serial_putc(make8(start_address,1));
   ModbusRTU_serial_putc(make8(start_address,0));

   ModbusRTU_serial_putc(make8(quantity,1));
   ModbusRTU_serial_putc(make8(quantity,0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
read_input_registers
Input:     int8       address            Slave Address
           int16      start_address      Address to start reading from
           int16      quantity           Amount of addresses to read
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_read_input_registers(uint8_t address, uint16_t start_address, uint16_t quantity)
{
   ModbusRTU_serial_send_start(address, FUNC_READ_INPUT_REGISTERS);

   ModbusRTU_serial_putc(make8(start_address,1));
   ModbusRTU_serial_putc(make8(start_address,0));

   ModbusRTU_serial_putc(make8(quantity,1));
   ModbusRTU_serial_putc(make8(quantity,0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
write_single_coil
Input:     int8       address            Slave Address
           int16      output_address     Address to write into
           int1       on                 true for on, false for off
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_write_single_coil(uint8_t address, uint16_t output_address, uint8_t on)
{
   ModbusRTU_serial_send_start(address, FUNC_WRITE_SINGLE_COIL);

   ModbusRTU_serial_putc(make8(output_address,1));
   ModbusRTU_serial_putc(make8(output_address,0));

   if(on)
       ModbusRTU_serial_putc(0xFF);
   else
       ModbusRTU_serial_putc(0x00);

   ModbusRTU_serial_putc(0x00);

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
write_single_register
Input:     int8       address            Slave Address
           int16      reg_address        Address to write into
           int16      reg_value          Value to write
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_write_single_register(uint8_t address, uint16_t reg_address, uint16_t reg_value)
{
   ModbusRTU_serial_send_start(address, FUNC_WRITE_SINGLE_REGISTER);

   ModbusRTU_serial_putc(make8(reg_address,1));
   ModbusRTU_serial_putc(make8(reg_address,0));

   ModbusRTU_serial_putc(make8(reg_value,1));
   ModbusRTU_serial_putc(make8(reg_value,0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
read_exception_status
Input:     int8       address            Slave Address
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_read_exception_status(uint8_t address)
{
   ModbusRTU_serial_send_start(address, FUNC_READ_EXCEPTION_STATUS);
   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
diagnostics
Input:     int8       address            Slave Address
           int16      sub_func           Subfunction to send
           int16      data               Data to send, changes based on subfunction
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_diagnostics(uint8_t address, uint16_t sub_func, uint16_t data)
{
   ModbusRTU_serial_send_start(address, FUNC_DIAGNOSTICS);

   ModbusRTU_serial_putc(make8(sub_func,1));
   ModbusRTU_serial_putc(make8(sub_func,0));

   ModbusRTU_serial_putc(make8(data,1));
   ModbusRTU_serial_putc(make8(data,0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
get_comm_event_couter
Input:     int8       address            Slave Address
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_get_comm_event_counter(uint8_t address)
{
   ModbusRTU_serial_send_start(address, FUNC_GET_COMM_EVENT_COUNTER);
   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
get_comm_event_log
Input:     int8       address            Slave Address
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_get_comm_event_log(uint8_t address)
{
   ModbusRTU_serial_send_start(address, FUNC_GET_COMM_EVENT_LOG);
   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
write_multiple_coils
Input:     int8       address            Slave Address
           int16      start_address      Address to start at
           int16      quantity           Amount of coils to write to
           int1*      values             A pointer to an array holding the values to write
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_write_multiple_coils(uint8_t address, uint16_t start_address, uint16_t quantity,
                           uint8_t *values)
{
   uint8_t i,count;

   count = (uint8_t)((quantity/8));

   if(quantity%8)
      count++;

   ModbusRTU_serial_send_start(address, FUNC_WRITE_MULTIPLE_COILS);

   ModbusRTU_serial_putc(make8(start_address,1));
   ModbusRTU_serial_putc(make8(start_address,0));

   ModbusRTU_serial_putc(make8(quantity,1));
   ModbusRTU_serial_putc(make8(quantity,0));

   ModbusRTU_serial_putc(count);

   for(i=0; i < count; ++i)
      ModbusRTU_serial_putc(values[i]);

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
write_multiple_registers
Input:     int8       address            Slave Address
           int16      start_address      Address to start at
           int16      quantity           Amount of coils to write to
           int16*     values             A pointer to an array holding the data to write
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_write_multiple_registers(uint8_t address, uint16_t start_address, uint16_t quantity,
                           uint16_t *values)
{
   uint8_t i,count;

   count = quantity*2;

   ModbusRTU_serial_send_start(address, FUNC_WRITE_MULTIPLE_REGISTERS);

   ModbusRTU_serial_putc(make8(start_address,1));
   ModbusRTU_serial_putc(make8(start_address,0));

   ModbusRTU_serial_putc(make8(quantity,1));
   ModbusRTU_serial_putc(make8(quantity,0));

   ModbusRTU_serial_putc(count);

   for(i=0; i < quantity; ++i)
   {
      ModbusRTU_serial_putc(make8(values[i],1));
      ModbusRTU_serial_putc(make8(values[i],0));
   }

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
report_slave_id
Input:     int8       address            Slave Address
Output:    exception                     0 if no error, else the exception
*/
void ModbusRTU_report_slave_id(uint8_t address)
{
   ModbusRTU_serial_send_start(address, FUNC_REPORT_SLAVE_ID);
   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}


/*
mask_write_register
Input:     int8       address            Slave Address
           int16      reference_address  Address to mask
           int16      AND_mask           A mask to AND with the data at reference_address
           int16      OR_mask            A mask to OR with the data at reference_address
Output:    exception                              0 if no error, else the exception
*/
void ModbusRTU_mask_write_register(uint8_t address, uint16_t reference_address,
                           uint16_t AND_mask, uint16_t OR_mask)
{
   ModbusRTU_serial_send_start(address, FUNC_MASK_WRITE_REGISTER);

   ModbusRTU_serial_putc(make8(reference_address,1));
   ModbusRTU_serial_putc(make8(reference_address,0));

   ModbusRTU_serial_putc(make8(AND_mask,1));
   ModbusRTU_serial_putc(make8(AND_mask,0));

   ModbusRTU_serial_putc(make8(OR_mask, 1));
   ModbusRTU_serial_putc(make8(OR_mask, 0));

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
read_write_multiple_registers
Input:     int8       address                Slave Address
           int16      read_start             Address to start reading
           int16      read_quantity          Amount of registers to read
           int16      write_start            Address to start writing
           int16      write_quantity         Amount of registers to write
           int16*     write_registers_value  Pointer to an aray us to write
Output:    exception                         0 if no error, else the exception
*/
void ModbusRTU_read_write_multiple_registers(uint8_t address, uint16_t read_start,
                                    uint16_t read_quantity, uint16_t write_start,
                                    uint16_t write_quantity,
                                    uint16_t *write_registers_value)
{
   uint8_t i;

   ModbusRTU_serial_send_start(address, FUNC_READ_WRITE_MULTIPLE_REGISTERS);

   ModbusRTU_serial_putc(make8(read_start,1));
   ModbusRTU_serial_putc(make8(read_start,0));

   ModbusRTU_serial_putc(make8(read_quantity,1));
   ModbusRTU_serial_putc(make8(read_quantity,0));

   ModbusRTU_serial_putc(make8(write_start, 1));
   ModbusRTU_serial_putc(make8(write_start, 0));

   ModbusRTU_serial_putc(make8(write_quantity, 1));
   ModbusRTU_serial_putc(make8(write_quantity, 0));

   ModbusRTU_serial_putc((uint8_t)(2*write_quantity));

   for(i=0; i < write_quantity ; i+=2)
   {
      ModbusRTU_serial_putc(make8(write_registers_value[i], 1));
      ModbusRTU_serial_putc(make8(write_registers_value[i+1], 0));
   }

   ModbusRTU_serial_send_stop();

    ModbusRTU_Send_On();

}

/*
read_FIFO_queue
Input:     int8       address           Slave Address
           int16      FIFO_address      FIFO address
Output:    exception                    0 if no error, else the exception
*/
void ModbusRTU_read_FIFO_queue(uint8_t address, uint16_t FIFO_address)
{
   ModbusRTU_serial_send_start(address, FUNC_READ_FIFO_QUEUE);

   ModbusRTU_serial_putc(make8(FIFO_address, 1));
   ModbusRTU_serial_putc(make8(FIFO_address, 0));

   ModbusRTU_serial_send_stop();

   ModbusRTU_Send_On();

}

void MemCopy8bit(char NumberByte, uint8_t * BeginAddress, uint8_t * DestinateAddress)
{
char i;
for(i=0;i<NumberByte;i++)
{
   DestinateAddress[i]=BeginAddress[i];
}

}

void MemCopy16bit(char NumberByte, uint16_t * BeginAddress, uint16_t * DestinateAddress)
{
char i;
for(i=0;i<NumberByte;i++)
{
   DestinateAddress[i]=BeginAddress[i];
}
}

#endif
