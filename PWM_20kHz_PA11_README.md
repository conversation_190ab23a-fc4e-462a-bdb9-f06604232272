# PWM 20kHz trên chân PA11 - STM32F030

## Tổng quan
Code này tạo tín hiệu PWM với tần số chính xác 20kHz trên chân PA11 của STM32F030C8T6.

## Thông số kỹ thuật
- **Tần số PWM**: 20.000 Hz (20kHz)
- **Resolution**: 1600 steps (0-1599)
- **Duty Cycle Range**: 0% - 100%
- **Pin**: PA11 (TIM1_CH4)
- **Timer**: TIM1
- **Prescaler**: 0 (chia 1)
- **Period**: 1599

## Tính toán tần số
```
Timer Clock = 32MHz
Prescaler = 0 (chia cho 1)
Period = 1599 (1600 steps)

PWM Frequency = 32,000,000 / (1 × 1600) = 20,000 Hz = 20kHz
```

## Cấu hình phần cứng
- **MCU**: STM32F030C8T6
- **System Clock**: 32MHz
- **Timer Clock**: 32MHz (không chia)
- **GPIO**: PA11 configured as AF2 (TIM1_CH4)

## API Functions

### `uint32_t PWM_Setup_20kHz_PA11(void)`
Cấu hình timer để tạo PWM 20kHz chính xác.

**Trả về:**
- Tần số thực tế (20000 Hz)

**Sử dụng:**
```c
uint32_t freq = PWM_Setup_20kHz_PA11();  // freq = 20000
```

### `void PWM_Start_PA11(void)`
Khởi động PWM trên chân PA11.

**Sử dụng:**
```c
PWM_Start_PA11();
```

### `void PWM_Stop_PA11(void)`
Dừng PWM trên chân PA11.

**Sử dụng:**
```c
PWM_Stop_PA11();
```

### `void PWM_SetDutyCycle_PA11(uint8_t duty_percent)`
Thiết lập duty cycle cho PWM.

**Tham số:**
- `duty_percent`: Phần trăm duty cycle (0-100)

**Sử dụng:**
```c
PWM_SetDutyCycle_PA11(25);   // 25% duty cycle (400/1600)
PWM_SetDutyCycle_PA11(50);   // 50% duty cycle (800/1600)
PWM_SetDutyCycle_PA11(75);   // 75% duty cycle (1200/1600)
```

## Ví dụ sử dụng

### Ví dụ 1: PWM cố định 20kHz
```c
int main(void)
{
    // Khởi tạo hệ thống...
    
    // Cấu hình PWM 20kHz
    PWM_Setup_20kHz_PA11();
    
    // Bật PWM với duty cycle 60%
    PWM_Start_PA11();
    PWM_SetDutyCycle_PA11(60);
    
    while(1)
    {
        // Code khác...
    }
}
```

### Ví dụ 2: Thay đổi duty cycle (như trong demo)
```c
int main(void)
{
    // Khởi tạo hệ thống...
    
    PWM_Setup_20kHz_PA11();
    PWM_Start_PA11();
    
    uint8_t duty = 10;
    uint8_t direction = 1;
    
    while(1)
    {
        PWM_SetDutyCycle_PA11(duty);
        
        if (direction) {
            duty += 10;
            if (duty >= 90) {
                duty = 90;
                direction = 0;
            }
        } else {
            duty -= 10;
            if (duty <= 10) {
                duty = 10;
                direction = 1;
            }
        }
        
        HAL_Delay(500);
    }
}
```

### Ví dụ 3: Điều khiển motor với PWM 20kHz
```c
void Motor_SetSpeed(uint8_t speed_percent)
{
    if (speed_percent == 0) {
        PWM_Stop_PA11();
    } else {
        PWM_SetDutyCycle_PA11(speed_percent);
        PWM_Start_PA11();
    }
}

int main(void)
{
    // Khởi tạo hệ thống...
    PWM_Setup_20kHz_PA11();
    
    // Tăng tốc từ 0% đến 100%
    for (uint8_t speed = 0; speed <= 100; speed += 5) {
        Motor_SetSpeed(speed);
        HAL_Delay(200);
    }
    
    // Giảm tốc từ 100% về 0%
    for (uint8_t speed = 100; speed > 0; speed -= 5) {
        Motor_SetSpeed(speed);
        HAL_Delay(200);
    }
    
    Motor_SetSpeed(0);  // Dừng motor
}
```

## Ứng dụng của PWM 20kHz
- **Motor DC**: Tần số cao giảm tiếng ồn và rung động
- **Switching Power Supply**: Ngoài tầm nghe của con người (>20kHz)
- **LED Dimming**: Không nhấp nháy, mắt không nhận ra
- **Audio PWM**: Tần số cao cho chất lượng âm thanh tốt
- **Fan Control**: Điều khiển quạt êm ái

## Ưu điểm của 20kHz
1. **Ngoài tầm nghe**: Không tạo tiếng ồn âm thanh
2. **Hiệu quả cao**: Ít tổn hao switching
3. **EMI thấp**: Ít nhiễu điện từ
4. **Điều khiển mượt**: Resolution cao (1600 steps)

## Lưu ý quan trọng
1. Chân PA11 phải được cấu hình đúng trong STM32CubeMX
2. Timer 1 phải được enable và cấu hình PWM mode
3. System clock phải là 32MHz để đạt chính xác 20kHz
4. Sau khi gọi PWM_Setup_20kHz_PA11(), cần gọi PWM_Start_PA11()

## Troubleshooting
- **Không có tín hiệu**: Kiểm tra cấu hình GPIO và Timer
- **Tần số sai**: Kiểm tra System Clock (phải là 32MHz)
- **Duty cycle không đúng**: Kiểm tra giá trị Period = 1599
- **Tín hiệu không ổn định**: Kiểm tra nguồn cấp và ground
