../Core/Src/syscalls.c:44:6:initialise_monitor_handles	8	static
../Core/Src/syscalls.c:48:5:_getpid	8	static
../Core/Src/syscalls.c:53:5:_kill	16	static
../Core/Src/syscalls.c:61:6:_exit	16	static
../Core/Src/syscalls.c:67:27:_read	32	static
../Core/Src/syscalls.c:80:27:_write	32	static
../Core/Src/syscalls.c:92:5:_close	16	static
../Core/Src/syscalls.c:99:5:_fstat	16	static
../Core/Src/syscalls.c:106:5:_isatty	16	static
../Core/Src/syscalls.c:112:5:_lseek	24	static
../Core/Src/syscalls.c:120:5:_open	20	static
../Core/Src/syscalls.c:128:5:_wait	16	static
../Core/Src/syscalls.c:135:5:_unlink	16	static
../Core/Src/syscalls.c:142:5:_times	16	static
../Core/Src/syscalls.c:148:5:_stat	16	static
../Core/Src/syscalls.c:155:5:_link	16	static
../Core/Src/syscalls.c:163:5:_fork	8	static
../Core/Src/syscalls.c:169:5:_execve	24	static
