../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:289:19:HAL_UART_Init	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:380:19:HAL_HalfDuplex_Init	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:582:19:HAL_MultiProcessor_Init	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:674:19:HAL_UART_DeInit	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:721:13:HAL_UART_MspInit	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:736:13:HAL_UART_MspDeInit	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1149:19:HAL_UART_Transmit	48	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1251:19:HAL_UART_Receive	48	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1351:19:HAL_UART_Transmit_IT	40	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1416:19:HAL_UART_Receive_IT	40	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1470:19:HAL_UART_Transmit_DMA	40	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1556:19:HAL_UART_Receive_DMA	40	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1600:19:HAL_UART_DMAPause	88	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1630:19:HAL_UART_DMAResume	80	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1661:19:HAL_UART_DMAStop	56	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1736:19:HAL_UART_Abort	96	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1833:19:HAL_UART_AbortTransmit	48	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1886:19:HAL_UART_AbortReceive	80	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:1954:19:HAL_UART_Abort_IT	104	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2101:19:HAL_UART_AbortTransmit_IT	48	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2186:19:HAL_UART_AbortReceive_IT	80	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2279:6:HAL_UART_IRQHandler	184	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2585:13:HAL_UART_TxCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2600:13:HAL_UART_TxHalfCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2615:13:HAL_UART_RxCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2630:13:HAL_UART_RxHalfCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2645:13:HAL_UART_ErrorCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2660:13:HAL_UART_AbortCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2675:13:HAL_UART_AbortTransmitCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2690:13:HAL_UART_AbortReceiveCpltCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2707:13:HAL_UARTEx_RxEventCallback	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2755:6:HAL_UART_ReceiverTimeout_Config	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2767:19:HAL_UART_EnableReceiverTimeout	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2798:19:HAL_UART_DisableReceiverTimeout	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2829:19:HAL_MultiProcessor_EnableMuteMode	32	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2849:19:HAL_MultiProcessor_DisableMuteMode	32	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2869:6:HAL_MultiProcessor_EnterMuteMode	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2879:19:HAL_HalfDuplex_EnableTransmitter	48	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2902:19:HAL_HalfDuplex_EnableReceiver	48	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2973:23:HAL_UART_GetState	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:2989:10:HAL_UART_GetError	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3037:19:UART_SetConfig	40	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3175:6:UART_AdvFeatureConfig	16	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3249:19:UART_CheckIdleState	80	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3318:19:UART_WaitOnFlagUntilTimeout	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3386:19:UART_Start_Receive_IT	72	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3435:19:UART_Start_Receive_DMA	72	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3492:13:UART_EndTxTransfer	32	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3507:13:UART_EndRxTransfer	64	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3533:13:UART_DMATransmitCplt	56	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3567:13:UART_DMATxHalfCplt	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3585:13:UART_DMAReceiveCplt	88	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3646:13:UART_DMARxHalfCplt	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3684:13:UART_DMAError	32	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3724:13:UART_DMAAbortOnError	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3747:13:UART_DMATxAbortCallback	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3797:13:UART_DMARxAbortCallback	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3849:13:UART_DMATxOnlyAbortCallback	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3877:13:UART_DMARxOnlyAbortCallback	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3910:13:UART_TxISR_8BIT	48	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3939:13:UART_TxISR_16BIT	56	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3971:13:UART_EndTransmit_IT	32	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:3996:13:UART_RxISR_8BIT	88	static,ignoring_inline_asm
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal_uart.c:4084:13:UART_RxISR_16BIT	88	static,ignoring_inline_asm
