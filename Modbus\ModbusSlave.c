#include "ModbusSlave.h"
#include "Crc_Caculator.h"



#ifdef MODBUS_SLAVE
osEventFlagsId_t evt_rtu_slave_rev_it_id; 
osEventFlagsId_t evt_rtu_slave_rev_id;   
osEventFlagsId_t evt_rtu_slave_complete_id; 

	extern UART_HandleTypeDef huart1;
	#define huartRTU_Slave huart1

	#define MODBUS_MEMORY
	//-------------------------------------------RTU_Slave define -------------------
	uint8_t BufferRTU_SlaveSend[200];
	uint8_t BufferRTU_SlaveRev[200];
	uint8_t RTU_SlaveRevNumberByte=0;

	extern union
	{
		 uint8_t b[2];
		 uint16_t d;
	} serial_crc;

	#ifdef MODBUS_MEMORY

		#define MAX_COILS_ADDRESS 20
		#define MAX_DISCRETE_INPUT_ADDRESS 20
		#define MAX_HOLDING_REGISTER_ADDRESS 400
		#define MAX_INPUT_REGISTER_ADDRESS 10
		
	// memory for configure mobuslave
	#define HOLDIING_COUNT_COMPLET 300

		// 1x data
		uint8_t RTUSlaveCoilsData[MAX_COILS_ADDRESS];
	// 1x data
		uint8_t RTUSlaveDiscreteInputData[MAX_DISCRETE_INPUT_ADDRESS];
	//4x data
		uint16_t RTUSlaveHoldingRegisterData[ MAX_HOLDING_REGISTER_ADDRESS];
	//3x data
		uint16_t RTUSlaveInputRegisterData[MAX_INPUT_REGISTER_ADDRESS];
		
void PutRTUHoldingRegister( char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs ){
		int	iRegIndex;
    if( ( usAddress >= 0 )
        && ( usAddress + usNRegs <= 0 + MAX_HOLDING_REGISTER_ADDRESS ) )
    {
        iRegIndex = ( int )( usAddress - 0 );
        while( usNRegs > 0 )
        {
            RTUSlaveHoldingRegisterData[iRegIndex] = *pucRegBuffer++;
            RTUSlaveHoldingRegisterData[iRegIndex] |= *pucRegBuffer++ << 8;
            iRegIndex++;
            usNRegs--;
        }
    }
}
void GetRTUHoldingRegister( char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs ){
		int	iRegIndex;
    if( ( usAddress >= 0 )
        && ( usAddress + usNRegs <= 0 + MAX_HOLDING_REGISTER_ADDRESS ) )
    {
        iRegIndex = ( int )( usAddress - 0 );
        while( usNRegs > 0 )
        {
             *pucRegBuffer++ = ( char ) ( RTUSlaveHoldingRegisterData[iRegIndex] & 0xFF  );
             *pucRegBuffer++ = ( char ) ( RTUSlaveHoldingRegisterData[iRegIndex] >> 8);
            iRegIndex++;
            usNRegs--;
        }
    }
}

void PutRTUInputRegister(  char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs ){
		int	iRegIndex;
    if( ( usAddress >= 0 )
        && ( usAddress + usNRegs <= 0 + MAX_INPUT_REGISTER_ADDRESS ) )
    {
        iRegIndex = ( int )( usAddress - 0 );
        while( usNRegs > 0 )
        {
            RTUSlaveInputRegisterData[iRegIndex] = *pucRegBuffer++ ;
            RTUSlaveInputRegisterData[iRegIndex] |= *pucRegBuffer++ << 8;
            iRegIndex++;
            usNRegs--;
        }
    }
}

void GetRTUInputRegister ( char * pucRegBuffer, uint16_t usAddress, uint16_t usNRegs ){
		int	iRegIndex;
    if( ( usAddress >= 0 )
        && ( usAddress + usNRegs <= 0 + MAX_INPUT_REGISTER_ADDRESS ) )
    {
        iRegIndex = ( int )( usAddress - 0 );
        while( usNRegs > 0 )
        {
             *pucRegBuffer++ = (  char ) ( RTUSlaveInputRegisterData[iRegIndex] & 0xFF  );
             *pucRegBuffer++ = (  char ) ( RTUSlaveInputRegisterData[iRegIndex] >> 8);
            iRegIndex++;
            usNRegs--;
        }
    }
}

	#else	
		// 1x data
			extern uint8_t CoilsData[];
		// 1x data
			extern uint8_t DiscreteInputData[];
		//4x data
			extern uint16_t HoldingRegisterData[];
		//3x data
			extern uint16_t InputRegisterData[];
	#endif	


	//--------------High Layer -------------------------////////////////
	RTU_SlaveData SlaveData;

	void ModbusRTU_SlaveInit(uint8_t SlaveAdd)
	{
		SlaveData.SlaveAddress = SlaveAdd;
		

	 #ifdef SERIAL_USE_ENABLE_PIN// enalble Receiver
		 ENABLE_RECEIVER;
	#endif
		 
	}


	//-----------------------------------------------------------------------------
	
	uint8_t BufferRTU_SlaveContinousRev[MAXBYTE_CONTINOUS_RECEIVER];
	uint8_t BufferRTU_SlaveContinousNumberByte=0;
	uint8_t MRS_CountRevByteDMA_N=MAXBYTE_CONTINOUS_RECEIVER, 
					MRS_CountRevByteDMA_N1=MAXBYTE_CONTINOUS_RECEIVER;

	//----------------------------------------------------------------------------
#ifdef USE_IDLE_LINE_SLAVE
	int CountWaitSlave=0;

bool StatusReceiverUart = false;
	
void ModbusRTU_SlaveTask(void)
{
		 if(huartRTU_Slave.hdmarx->State != HAL_DMA_STATE_BUSY)
		 {
				StatusReceiverUart = false;
		 }	
		 
		if(StatusReceiverUart == false){
			
			if(HAL_UARTEx_ReceiveToIdle_DMA(&huartRTU_Slave, BufferRTU_SlaveContinousRev, MAXBYTE_CONTINOUS_RECEIVER) == HAL_OK){
					__HAL_DMA_DISABLE_IT( huartRTU_Slave.hdmarx, DMA_IT_HT);
					StatusReceiverUart = true;
			}else{
					HAL_UART_AbortReceive_IT(&huartRTU_Slave);
			}
			
		}
		// =============
		if(osEventFlagsWait(evt_rtu_slave_rev_it_id, FLAGS_MSG_SLAVE_RTU_REV_IT, osFlagsWaitAny, 10000)== FLAGS_MSG_SLAVE_RTU_REV_IT){// 100ms
					if(ModbusRTU_Slave_CheckMessage() == 1)// message Right
					{
							osEventFlagsSet(evt_rtu_slave_rev_id, FLAGS_MSG_SLAVE_RTU_REV);
						
							osEventFlagsWait(evt_rtu_slave_complete_id, FLAGS_MSG_SLAVE_RTU_COMPLETE, osFlagsWaitAny, 200);// 100ms

							ModbusRTU_SlaveProcessing(&SlaveData);//Processing message
					}
		}			
}
#else
	int CountWaitSlave=0;

void ModbusRTU_SlaveTask(void)
{
			if(SlaveData.Send_On_Flag ==1)// if error and n't have Interrupt on transmit  
			{
				 CountWaitSlave++;
				 if(CountWaitSlave>3){
						CountWaitSlave=0;
						 #ifdef SERIAL_USE_ENABLE_PIN// enalble Receiver
							 ENABLE_RECEIVER;
						#endif		 
						 SlaveData.Send_On_Flag =0;
				 }
			}else{
					CountWaitSlave=0;
			}
				 
			if(ModbusRTU_Slave_Wait_Message() == 1)// have New message
			{
					
					if(ModbusRTU_Slave_CheckMessage() == 1)// message Right
					{
							osEventFlagsSet(evt_rtu_slave_rev_id, FLAGS_MSG_SLAVE_RTU_REV);
						
							osEventFlagsWait(evt_rtu_slave_complete_id, FLAGS_MSG_SLAVE_RTU_COMPLETE, osFlagsWaitAny, 200);// 100ms
						
							ModbusRTU_SlaveProcessing(&SlaveData);//Processing message
					}
			}			
			ModbusRTU_Slave_Enable_Receiver();
}

uint8_t ModbusRTU_Slave_Wait_Message(void)
{	
		 MRS_CountRevByteDMA_N=__HAL_DMA_GET_COUNTER((huartRTU_Slave).hdmarx);

		 if((MRS_CountRevByteDMA_N!=MAXBYTE_CONTINOUS_RECEIVER) && (MRS_CountRevByteDMA_N1 == MRS_CountRevByteDMA_N))
		 {// end block check
				HAL_UART_AbortReceive_IT(&huartRTU_Slave);// stop receiver
				// New Process

				BufferRTU_SlaveContinousNumberByte=MAXBYTE_CONTINOUS_RECEIVER- MRS_CountRevByteDMA_N;
				return 1;
		 }else	{	
				MRS_CountRevByteDMA_N1 = MRS_CountRevByteDMA_N;
				return 0;
		 }
}


void ModbusRTU_Slave_Enable_Receiver(void)
{
		 if(huartRTU_Slave.hdmarx->State != HAL_DMA_STATE_BUSY)
		 {

				if(HAL_UART_Receive_DMA(&huartRTU_Slave,
					BufferRTU_SlaveContinousRev, MAXBYTE_CONTINOUS_RECEIVER)== HAL_OK)
				{
					MRS_CountRevByteDMA_N=MAXBYTE_CONTINOUS_RECEIVER;
					MRS_CountRevByteDMA_N1=MAXBYTE_CONTINOUS_RECEIVER;
				}
				else
				{
					HAL_UART_AbortReceive_IT(&huartRTU_Slave);
				}		
		
		 }
}
#endif	

int8_t byte_high_temp=0, byte_low_temp=0;
int8_t byte_high_humidity=0, byte_low_temp_humidity=0;
int16_t temp=0, humidity=0;
uint8_t ModbusRTU_Slave_CheckMessage(void)
{
			uint8_t i;
						for(i=0;i<BufferRTU_SlaveContinousNumberByte;i++)
						{
								BufferRTU_SlaveRev[i]=BufferRTU_SlaveContinousRev[i];
						}
						if(BufferRTU_SlaveRev[0]== 31 && BufferRTU_SlaveRev[2]== 0x04  && CheckFarm(BufferRTU_SlaveRev,BufferRTU_SlaveContinousNumberByte)==1) 
						{	
							byte_high_temp = BufferRTU_SlaveRev[3]; 
							byte_low_temp  = BufferRTU_SlaveRev[4];
							//byte_high_humidity     = BufferRTU_SlaveRev[5];
							//byte_low_temp_humidity = BufferRTU_SlaveRev[6];
							temp = (byte_high_temp<<8)|byte_low_temp;
							//humidity = (byte_high_humidity<<8)|byte_low_temp_humidity;
							PutRTUInputRegister((char *)&temp,0x01,1);
							
						}	
						if(BufferRTU_SlaveRev[0]== 32 && BufferRTU_SlaveRev[2]== 0x04  && CheckFarm(BufferRTU_SlaveRev,BufferRTU_SlaveContinousNumberByte)==1) 
						{	
							byte_high_temp = BufferRTU_SlaveRev[3]; 
							byte_low_temp  = BufferRTU_SlaveRev[4];
							//byte_high_humidity     = BufferRTU_SlaveRev[5];
							//byte_low_temp_humidity = BufferRTU_SlaveRev[6];
							temp = byte_high_temp<<8|byte_low_temp;
							//humidity = byte_high_humidity<<8|byte_low_temp_humidity;
							PutRTUInputRegister((char *)&temp,0x03,1);
						}
						if(BufferRTU_SlaveRev[0]== 33 && BufferRTU_SlaveRev[2]== 0x04  && CheckFarm(BufferRTU_SlaveRev,BufferRTU_SlaveContinousNumberByte)==1) 
						{	
							byte_high_temp = BufferRTU_SlaveRev[3]; 
							byte_low_temp  = BufferRTU_SlaveRev[4];
							//byte_high_humidity     = BufferRTU_SlaveRev[5];
							//byte_low_temp_humidity = BufferRTU_SlaveRev[6];
							temp = byte_high_temp<<8|byte_low_temp;
							//humidity = byte_high_humidity<<8|byte_low_temp_humidity;
							PutRTUInputRegister((char *)&temp,0x04,1);
						}
						if(BufferRTU_SlaveRev[0]== 34 && BufferRTU_SlaveRev[2]== 0x04  && CheckFarm(BufferRTU_SlaveRev,BufferRTU_SlaveContinousNumberByte)==1) 
						{	
							byte_high_temp = BufferRTU_SlaveRev[3]; 
							byte_low_temp  = BufferRTU_SlaveRev[4];
							//byte_high_humidity     = BufferRTU_SlaveRev[5];
							//byte_low_temp_humidity = BufferRTU_SlaveRev[6];
							temp = byte_high_temp<<8|byte_low_temp;
							//humidity = byte_high_humidity<<8|byte_low_temp_humidity;
							PutRTUInputRegister((char *)&temp,0x05,1);
						}							
						if(BufferRTU_SlaveRev[0]== SlaveData.SlaveAddress && CheckFarm(BufferRTU_SlaveRev,BufferRTU_SlaveContinousNumberByte)==1) 
						{// crc check
							// Transfer mem to mem

							SlaveData.LengthOfReceiveMessage = BufferRTU_SlaveContinousNumberByte;
							SlaveData.RevCompleteEvent=1;
							
							return 1;
						}else return 0;

}
	

void ModbusRTU_SlaveProcessing(RTU_SlaveData* SlaveData )
{
		 int16_t NumberCoilRequest;
		 int16_t AddressCoilRequest;
		 int16_t AddressEndCoilRequest   ;
		 char CoilNBytes;
		 int16_t NumberDiscrteteInputRequest;
		 int16_t AddressDiscrteteInputRequest;
		 int16_t AddressEndDiscrteteInputRequest;
		 char DiscrteteInputNBytes;
		 int16_t NumberMultipleCoilRequest;
		 int16_t AddressMultipleCoilRequest;
		 int16_t AddressMultipleRegisterRequest;
		 
		 int16_t NumberHoldingRegisterRequest;
		 int16_t AddressHoldingRegisterRequest;
		 int16_t NumberInputRegisterRequest;
		 int16_t AddressInputRegisterRequest;
		 int16_t AddressSingleRegisterRequest ;
		 int i,j,k=0;
		 uint8_t RTU_Slave_ADDRESS;
	
	if(SlaveData->RevCompleteEvent==1)
	{

		 RTU_Slave_ADDRESS=SlaveData->SlaveAddress;		
		
		 switch(BufferRTU_SlaveRev[1]) // function Code
		 {
							 // -----------------------------COIL function ---------------------
		 case FUNC_READ_COILS:    

					NumberCoilRequest = ((int16_t)(BufferRTU_SlaveRev[4])<<8) + BufferRTU_SlaveRev[5];
					AddressCoilRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];
					AddressEndCoilRequest = AddressCoilRequest +  NumberCoilRequest-1;
						 
						CoilNBytes =(AddressEndCoilRequest>>3) - (AddressCoilRequest>>3) + 1;
		 
					if(	( 	(AddressEndCoilRequest>>3) + CoilNBytes < MAX_COILS_ADDRESS)){ 
						SlaveData->Send_On_Flag =1;
						RTU_Slave_read_coils_rsp(RTU_Slave_ADDRESS, CoilNBytes, & RTUSlaveCoilsData[AddressCoilRequest>>3]);
					}else{
							goto SendError;
						}		
		break;  
		 case FUNC_WRITE_SINGLE_COIL:      //0X reference
		 
					 AddressCoilRequest = (((int16_t)(BufferRTU_SlaveRev[2]))<<8)
															+ BufferRTU_SlaveRev[3];
					 if(		(AddressCoilRequest>>3) < MAX_COILS_ADDRESS){ 
						 
						 if(BufferRTU_SlaveRev[4] == 0xFF)
						 {// write to memory
								bit_set(RTUSlaveCoilsData[AddressCoilRequest>>3] ,(AddressCoilRequest%8));
						 }
						 else
						 {
								bit_clear(RTUSlaveCoilsData[AddressCoilRequest>>3] ,(AddressCoilRequest%8));
						 }
						 
						 SlaveData->Send_On_Flag =1;				 
						 RTU_Slave_write_single_coil_rsp(RTU_Slave_ADDRESS, AddressCoilRequest,
												(((int16_t)BufferRTU_SlaveRev[4])<<8));
										 
					}else{
							goto SendError;
						}
		 break;   
		case FUNC_WRITE_MULTIPLE_COILS:
					 
				NumberMultipleCoilRequest = ((int16_t)(BufferRTU_SlaveRev[4])<<8) + BufferRTU_SlaveRev[5];
				AddressMultipleCoilRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];
				if(		((AddressMultipleCoilRequest>>3) + (NumberMultipleCoilRequest>>3)) < MAX_COILS_ADDRESS){ 
					 for(i=0,j=(AddressMultipleCoilRequest%8); i < NumberMultipleCoilRequest; ++i,++j)
					 {
							if(j==8){k++; j=0;}
							if(bit_test(BufferRTU_SlaveRev[k+7],j))
							{
								 bit_set(RTUSlaveCoilsData[(AddressMultipleCoilRequest>>3)+k],j);
							}
						 else
						 {
								 bit_clear(RTUSlaveCoilsData[(AddressMultipleCoilRequest>>3)+k],j);
						 }
					 }
					 SlaveData->Send_On_Flag =1;	
					 RTU_Slave_write_multiple_coils_rsp(RTU_Slave_ADDRESS, AddressMultipleCoilRequest, NumberMultipleCoilRequest);
										 
				 }else{
							goto SendError;
						}
		 break;
										 
		 case FUNC_READ_DISCRETE_INPUT:    //1X references
		 

					NumberDiscrteteInputRequest = ((int16_t)(BufferRTU_SlaveRev[4])<<8) + BufferRTU_SlaveRev[5];
					AddressDiscrteteInputRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];
					AddressEndDiscrteteInputRequest = AddressDiscrteteInputRequest +  NumberDiscrteteInputRequest;
							
					DiscrteteInputNBytes =(AddressEndDiscrteteInputRequest>>3) - (AddressDiscrteteInputRequest>>3) + 1;
					
					if(		((AddressDiscrteteInputRequest>>3) + DiscrteteInputNBytes) < MAX_DISCRETE_INPUT_ADDRESS){ 
						
							SlaveData->Send_On_Flag =1;	
							RTU_Slave_read_discrete_input_rsp(RTU_Slave_ADDRESS, DiscrteteInputNBytes,
										&RTUSlaveDiscreteInputData[AddressDiscrteteInputRequest>>3]);					
					}else{
							goto SendError;
						}
		 break;
		 case FUNC_READ_HOLDING_REGISTERS://4X references
		 
					NumberHoldingRegisterRequest = ((int16_t)(BufferRTU_SlaveRev[4])<<8) + BufferRTU_SlaveRev[5];
					AddressHoldingRegisterRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];  
					if((AddressHoldingRegisterRequest + NumberHoldingRegisterRequest) <MAX_HOLDING_REGISTER_ADDRESS){
						
						 SlaveData->Send_On_Flag =1;							 
							RTU_Slave_read_holding_registers_rsp(RTU_Slave_ADDRESS, NumberHoldingRegisterRequest<<1 ,
									&RTUSlaveHoldingRegisterData[AddressHoldingRegisterRequest] );
						
					}else{
							goto SendError;
						}
		 break;  
		 case FUNC_WRITE_MULTIPLE_REGISTERS:// 4x 

					 AddressMultipleRegisterRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];
					
					if((AddressMultipleRegisterRequest + (BufferRTU_SlaveRev[6]>>1)) <MAX_HOLDING_REGISTER_ADDRESS){
						
						 for(i=0,j=7; i < (BufferRTU_SlaveRev[6]>>1); ++i,j+=2)
						 {
									 RTUSlaveHoldingRegisterData[AddressMultipleRegisterRequest] = make16(BufferRTU_SlaveRev[j],BufferRTU_SlaveRev[j+1]);
									 AddressMultipleRegisterRequest++;
							}
						 
						 SlaveData->Send_On_Flag =1;					 
							RTU_Slave_write_multiple_registers_rsp(RTU_Slave_ADDRESS,
						 make16(BufferRTU_SlaveRev[2],BufferRTU_SlaveRev[3]),
						 make16(BufferRTU_SlaveRev[4],BufferRTU_SlaveRev[5]));
												
					}	else{
							goto SendError;
						}
		 break;                      
		 case FUNC_WRITE_SINGLE_REGISTER: //4X reference

					 AddressSingleRegisterRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];
		 
					if(AddressSingleRegisterRequest <MAX_HOLDING_REGISTER_ADDRESS){
						
						 RTUSlaveHoldingRegisterData[AddressSingleRegisterRequest] = make16(BufferRTU_SlaveRev[4],BufferRTU_SlaveRev[5]);// write to memory
			 
						 SlaveData->Send_On_Flag =1;	
						 RTU_Slave_write_single_register_rsp(RTU_Slave_ADDRESS, AddressSingleRegisterRequest,  RTUSlaveHoldingRegisterData[AddressSingleRegisterRequest] );
							
					}else{
							goto SendError;
						}		
		 break;                      
		 case FUNC_READ_INPUT_REGISTERS: // 3X references
													
							NumberInputRegisterRequest = ((int16_t)(BufferRTU_SlaveRev[4])<<8) + BufferRTU_SlaveRev[5];
						
							AddressInputRegisterRequest = ((int16_t)(BufferRTU_SlaveRev[2])<<8) + BufferRTU_SlaveRev[3];  
		 
						if((AddressInputRegisterRequest + NumberInputRegisterRequest) <MAX_INPUT_REGISTER_ADDRESS){
							
									SlaveData->Send_On_Flag =1;						
									RTU_Slave_read_input_registers_rsp(RTU_Slave_ADDRESS, NumberInputRegisterRequest<<1 ,
												&RTUSlaveInputRegisterData[AddressInputRegisterRequest] );
							
						}else{
							goto SendError;
						}
																											
		 break;

		 default:    //We don't support the function, so return exception
			 SendError:
				SlaveData->Send_On_Flag =1;	
				RTU_Slave_exception_rsp(RTU_Slave_ADDRESS,
							BufferRTU_SlaveRev[1],ILLEGAL_FUNCTION);
								 
		 }
		 
			SlaveData->RevCompleteEvent=0;
	 }
}

	// ---------------------------Put to the buffer -----------------------------
	//---------------------------------------------------------------------------
	unsigned char CountRTU_Slave=0;

	void RTU_Slave_serial_putc(uint8_t c)
	{
		 BufferRTU_SlaveSend[CountRTU_Slave]=c;
				CountRTU_Slave++;
		 calc_crc(c);
	}

	// Purpose:    Send a message over the RS485 bus
	// Inputs:     1) The destination address
	//             2) The number of bytes of data to send
	//             3) A pointer to the data to send
	//             4) The length of the data
	// Outputs:    TRUE if successful
	//             FALSE if failed
	// Note:       Format:  source | destination | data-length | data | checksum
	void RTU_Slave_serial_send_start(uint8_t to, uint8_t func)
	{
		 serial_crc.d=0xFFFF;
			CountRTU_Slave=0;

		 RTU_Slave_serial_putc(to);
		 RTU_Slave_serial_putc(func);
	}

	// Purpose:    Ends a message over the RS485 Bus
	// Inputs:     Character
	// Outputs:    None
	void RTU_Slave_serial_send_stop(void)
	{
		 uint8_t crc_low, crc_high;

		 crc_high=serial_crc.b[1];
		 crc_low=serial_crc.b[0];

		 RTU_Slave_serial_putc(crc_high);
		 RTU_Slave_serial_putc(crc_low);


	}

	void RTU_Slave_Send_On(void)// send data
	{
		
	#ifdef SERIAL_USE_ENABLE_PIN// enalble Tranmiter
		 DISABLE_RECEIVER;
	#endif
		
		 if(HAL_UART_Transmit_DMA(&huartRTU_Slave, BufferRTU_SlaveSend, CountRTU_Slave)!=HAL_OK)
		 {
				#ifdef SERIAL_USE_ENABLE_PIN
					ENABLE_RECEIVER;
				#endif 
		 }
		
	}


	//- --------------------------------------Respond function ------------------------------

	void RTU_Slave_read_coils_rsp(uint8_t address, uint8_t byte_count, uint8_t* coil_data)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_READ_COILS);

		 RTU_Slave_serial_putc(byte_count);

		 for(i=0; i < byte_count; ++i)
		 {
				RTU_Slave_serial_putc(*coil_data);
				coil_data++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	read_discrete_input_rsp
	Input:     int8       address            Slave Address
						 int8       byte_count         Number of bytes being sent
						 int8*      input_data         Pointer to an array of data to send
	Output:    void
	*/
	void RTU_Slave_read_discrete_input_rsp(uint8_t address, uint8_t byte_count,
																			uint8_t *input_data)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_READ_DISCRETE_INPUT);

		 RTU_Slave_serial_putc(byte_count);

		 for(i=0; i < byte_count; ++i)
		 {
				RTU_Slave_serial_putc(*input_data);
				input_data++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	read_holding_registers_rsp
	Input:     int8       address            Slave Address
						 int8       byte_count         Number of bytes being sent
						 int8*      reg_data           Pointer to an array of data to send
	Output:    void
	*/
	void RTU_Slave_read_holding_registers_rsp(uint8_t address, uint8_t byte_count,
																					uint16_t *reg_data)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_READ_HOLDING_REGISTERS);

		 RTU_Slave_serial_putc(byte_count);

		 for(i=0; i < byte_count; i+=2)
		 {
				RTU_Slave_serial_putc(make8(*reg_data,1));
				RTU_Slave_serial_putc(make8(*reg_data,0));
				reg_data++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	read_input_registers_rsp
	Input:     int8       address            Slave Address
						 int8       byte_count         Number of bytes being sent
						 int8*      input_data         Pointer to an array of data to send
	Output:    void
	*/
	void RTU_Slave_read_input_registers_rsp(uint8_t address, uint8_t byte_count,
																					uint16_t *input_data)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_READ_INPUT_REGISTERS);

		 RTU_Slave_serial_putc(byte_count);

		 for(i=0; i < byte_count; i+=2)
		 {
				RTU_Slave_serial_putc(make8(*input_data,1));
				RTU_Slave_serial_putc(make8(*input_data,0));
				input_data++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	write_single_coil_rsp
	Input:     int8       address            Slave Address
						 int16      output_address     Echo of output address received
						 int16      output_value       Echo of output value received
	Output:    void
	*/
	void RTU_Slave_write_single_coil_rsp(uint8_t address, uint16_t output_address,
																			uint16_t output_value)
	{
		 RTU_Slave_serial_send_start(address, FUNC_WRITE_SINGLE_COIL);

		 RTU_Slave_serial_putc(make8(output_address,1));
		 RTU_Slave_serial_putc(make8(output_address,0));

		 RTU_Slave_serial_putc(make8(output_value,1));
		 RTU_Slave_serial_putc(make8(output_value,0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	write_single_register_rsp
	Input:     int8       address            Slave Address
						 int16      reg_address        Echo of register address received
						 int16      reg_value          Echo of register value received
	Output:    void
	*/
	void RTU_Slave_write_single_register_rsp(uint8_t address, uint16_t reg_address,
																					uint16_t reg_value)
	{
		 RTU_Slave_serial_send_start(address, FUNC_WRITE_SINGLE_REGISTER);

		 RTU_Slave_serial_putc(make8(reg_address,1));
		 RTU_Slave_serial_putc(make8(reg_address,0));

		 RTU_Slave_serial_putc(make8(reg_value,1));
		 RTU_Slave_serial_putc(make8(reg_value,0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	read_exception_status_rsp
	Input:     int8       address            Slave Address
	Output:    void
	*/
	void RTU_Slave_read_exception_status_rsp(uint8_t address, uint8_t data)
	{
		 RTU_Slave_serial_send_start(address, FUNC_READ_EXCEPTION_STATUS);
		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	diagnostics_rsp
	Input:     int8       address            Slave Address
						 int16      sub_func           Echo of sub function received
						 int16      data               Echo of data received
	Output:    void
	*/
	void RTU_Slave_diagnostics_rsp(uint8_t address, uint16_t sub_func, uint16_t data)
	{
		 RTU_Slave_serial_send_start(address, FUNC_DIAGNOSTICS);

		 RTU_Slave_serial_putc(make8(sub_func,1));
		 RTU_Slave_serial_putc(make8(sub_func,0));

		 RTU_Slave_serial_putc(make8(data,1));
		 RTU_Slave_serial_putc(make8(data,0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	get_comm_event_counter_rsp
	Input:     int8       address            Slave Address
						 int16      status             Status, refer to RTU_Slave documentation
						 int16      event_count        Count of events
	Output:    void
	*/
	void RTU_Slave_get_comm_event_counter_rsp(uint8_t address, uint16_t status,
																					uint16_t event_count)
	{
		 RTU_Slave_serial_send_start(address, FUNC_GET_COMM_EVENT_COUNTER);

		 RTU_Slave_serial_putc(make8(status, 1));
		 RTU_Slave_serial_putc(make8(status, 0));

		 RTU_Slave_serial_putc(make8(event_count, 1));
		 RTU_Slave_serial_putc(make8(event_count, 0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	get_comm_event_counter_rsp
	Input:     int8       address            Slave Address
						 int16      status             Status, refer to RTU_Slave documentation
						 int16      event_count        Count of events
						 int16      message_count      Count of messages
						 int8*      events             Pointer to event data
						 int8       events_len         Length of event data in bytes
	Output:    void
	*/
	void RTU_Slave_get_comm_event_log_rsp(uint8_t address, uint16_t status,
																			uint16_t event_count, uint16_t message_count,
																			uint8_t *events, uint8_t events_len)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_GET_COMM_EVENT_LOG);

		 RTU_Slave_serial_putc(events_len+6);

		 RTU_Slave_serial_putc(make8(status, 1));
		 RTU_Slave_serial_putc(make8(status, 0));

		 RTU_Slave_serial_putc(make8(event_count, 1));
		 RTU_Slave_serial_putc(make8(event_count, 0));

		 RTU_Slave_serial_putc(make8(message_count, 1));
		 RTU_Slave_serial_putc(make8(message_count, 0));

		 for(i=0; i < events_len; ++i)
		 {
				RTU_Slave_serial_putc(*events);
				events++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	write_multiple_coils_rsp
	Input:     int8       address            Slave Address
						 int16      start_address      Echo of address to start at
						 int16      quantity           Echo of amount of coils written to
	Output:    void
	*/
	void RTU_Slave_write_multiple_coils_rsp(uint8_t address, uint16_t start_address,
																					uint16_t quantity)
	{
		 RTU_Slave_serial_send_start(address, FUNC_WRITE_MULTIPLE_COILS);

		 RTU_Slave_serial_putc(make8(start_address,1));
		 RTU_Slave_serial_putc(make8(start_address,0));

		 RTU_Slave_serial_putc(make8(quantity,1));
		 RTU_Slave_serial_putc(make8(quantity,0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	write_multiple_registers_rsp
	Input:     int8       address            Slave Address
						 int16      start_address      Echo of address to start at
						 int16      quantity           Echo of amount of registers written to
	Output:    void
	*/
	void RTU_Slave_write_multiple_registers_rsp(uint8_t address, uint16_t start_address,
																							uint16_t quantity)
	{
		 RTU_Slave_serial_send_start(address, FUNC_WRITE_MULTIPLE_REGISTERS);

		 RTU_Slave_serial_putc(make8(start_address,1));
		 RTU_Slave_serial_putc(make8(start_address,0));

		 RTU_Slave_serial_putc(make8(quantity,1));
		 RTU_Slave_serial_putc(make8(quantity,0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	report_slave_id_rsp
	Input:     int8       address            Slave Address
						 int8       slave_id           Slave Address
						 int8       run_status         Are we running?
						 int8*      data               Pointer to an array holding the data
						 int8       data_len           Length of data in bytes
	Output:    void
	*/
	void RTU_Slave_report_slave_id_rsp(uint8_t address, uint8_t slave_id, uint8_t run_status,
																uint8_t *data, uint8_t data_len)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_REPORT_SLAVE_ID);

		 RTU_Slave_serial_putc(data_len+2);
		 RTU_Slave_serial_putc(slave_id);

		 if(run_status)
			RTU_Slave_serial_putc(0xFF);
		 else
			RTU_Slave_serial_putc(0x00);

		 for(i=0; i < data_len; ++i)
		 {
				RTU_Slave_serial_putc(*data);
				data++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	read_file_record_rsp
	Input:     int8                     address            Slave Address
						 int8                     byte_count         Number of bytes to send
						 read_sub_request_rsp*    request            Structure holding record/data information
	Output:    void
	*/
	void RTU_Slave_read_file_record_rsp(uint8_t address, uint8_t byte_count,
																			RTU_Slave_read_sub_request_rsp *request)
	{
		 uint8_t i=0,j;

		 RTU_Slave_serial_send_start(address, FUNC_READ_FILE_RECORD);

		 RTU_Slave_serial_putc(byte_count);

		 while(i < byte_count)
		 {
				RTU_Slave_serial_putc(request->record_length);
				RTU_Slave_serial_putc(request->reference_type);

				for(j=0; (j < request->record_length); j+=2)
				{
					 RTU_Slave_serial_putc(make8(request->data[j], 1));
					 RTU_Slave_serial_putc(make8(request->data[j], 0));
				}

				i += (request->record_length)+1;
				request++;
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	write_file_record_rsp
	Input:     int8                     address            Slave Address
						 int8                     byte_count         Echo of number of bytes sent
						 write_sub_request_rsp*   request            Echo of Structure holding record information
	Output:    void
	*/
	void RTU_Slave_write_file_record_rsp(uint8_t address, uint8_t byte_count,
																			RTU_Slave_write_sub_request_rsp *request)
	{
		 uint8_t i, j=0;

		 RTU_Slave_serial_send_start(address, FUNC_WRITE_FILE_RECORD);

		 RTU_Slave_serial_putc(byte_count);

		 for(i=0; i < byte_count; i+=(7+(j*2)))
		 {
				RTU_Slave_serial_putc(request->reference_type);
				RTU_Slave_serial_putc(make8(request->file_number, 1));
				RTU_Slave_serial_putc(make8(request->file_number, 0));
				RTU_Slave_serial_putc(make8(request->record_number, 1));
				RTU_Slave_serial_putc(make8(request->record_number, 0));
				RTU_Slave_serial_putc(make8(request->record_length, 1));
				RTU_Slave_serial_putc(make8(request->record_length, 0));

				for(j=0; (j < request->record_length); j+=2)
				{
					 RTU_Slave_serial_putc(make8(request->data[j], 1));
					 RTU_Slave_serial_putc(make8(request->data[j], 0));
				}
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	mask_write_register_rsp
	Input:     int8        address            Slave Address
						 int16       reference_address  Echo of reference address
						 int16       AND_mask           Echo of AND mask
						 int16       OR_mask            Echo or OR mask
	Output:    void
	*/
	void RTU_Slave_mask_write_register_rsp(uint8_t address, uint16_t reference_address,
														 uint16_t AND_mask, uint16_t OR_mask)
	{
		 RTU_Slave_serial_send_start(address, FUNC_MASK_WRITE_REGISTER);

		 RTU_Slave_serial_putc(make8(reference_address,1));
		 RTU_Slave_serial_putc(make8(reference_address,0));

		 RTU_Slave_serial_putc(make8(AND_mask,1));
		 RTU_Slave_serial_putc(make8(AND_mask,0));

		 RTU_Slave_serial_putc(make8(OR_mask, 1));
		 RTU_Slave_serial_putc(make8(OR_mask, 0));

		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

	/*
	read_write_multiple_registers_rsp
	Input:     int8        address            Slave Address
						 int16*      data               Pointer to an array of data
						 int8        data_len           Length of data in bytes
	Output:    void
	*/
	void RTU_Slave_read_write_multiple_registers_rsp(uint8_t address, uint8_t data_len,
																									uint16_t *data)
	{
		 uint8_t i;

		 RTU_Slave_serial_send_start(address, FUNC_READ_WRITE_MULTIPLE_REGISTERS);

		 RTU_Slave_serial_putc(data_len*2);

		 for(i=0; i < data_len*2; i+=2)
		 {
				RTU_Slave_serial_putc(make8(data[i], 1));
				RTU_Slave_serial_putc(make8(data[i], 0));
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	/*
	read_FIFO_queue_rsp
	Input:     int8        address            Slave Address
						 int16       FIFO_len           Length of FIFO in bytes
						 int16*      data               Pointer to an array of data
	Output:    void
	*/
	void RTU_Slave_read_FIFO_queue_rsp(uint8_t address, uint16_t FIFO_len, uint16_t *data)
	{
		 uint8_t i;
		 uint16_t byte_count;

		 byte_count = ((FIFO_len*2)+2);

		 RTU_Slave_serial_send_start(address, FUNC_READ_FIFO_QUEUE);

		 RTU_Slave_serial_putc(make8(byte_count, 1));
		 RTU_Slave_serial_putc(make8(byte_count, 0));

		 RTU_Slave_serial_putc(make8(FIFO_len, 1));
		 RTU_Slave_serial_putc(make8(FIFO_len, 0));

		 for(i=0; i < FIFO_len; i+=2)
		 {
				RTU_Slave_serial_putc(make8(data[i], 1));
				RTU_Slave_serial_putc(make8(data[i], 0));
		 }

		 RTU_Slave_serial_send_stop();
		 
		 RTU_Slave_Send_On(); 
	}

	void RTU_Slave_exception_rsp(uint8_t address, uint16_t func, exception error)
	{
		 RTU_Slave_serial_send_start(address, func|0x80);
		 RTU_Slave_serial_putc(error);
		 RTU_Slave_serial_send_stop();
		
		RTU_Slave_Send_On(); 
	}

#endif
