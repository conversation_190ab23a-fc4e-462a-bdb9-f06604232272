../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:141:19:HAL_Init	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:165:19:H<PERSON>_DeInit	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:188:13:HAL_MspInit	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:199:13:HAL_MspDeInit	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:222:26:HAL_InitTick	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:281:13:HAL_IncTick	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:292:17:<PERSON><PERSON>_GetTick	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:301:10:HAL_GetTickPrio	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:310:19:HAL_SetTickFreq	40	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:343:21:HAL_GetTickFreq	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:359:13:HAL_Delay	24	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:385:13:HAL_SuspendTick	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:402:13:HAL_ResumeTick	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:412:10:HAL_GetHalVersion	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:421:10:HAL_GetREVID	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:430:10:HAL_GetDEVID	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:439:10:HAL_GetUIDw0	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:448:10:HAL_GetUIDw1	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:457:10:HAL_GetUIDw2	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:466:6:HAL_DBGMCU_EnableDBGStopMode	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:475:6:HAL_DBGMCU_DisableDBGStopMode	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:484:6:HAL_DBGMCU_EnableDBGStandbyMode	8	static
../Drivers/STM32F0xx_HAL_Driver/Src/stm32f0xx_hal.c:493:6:HAL_DBGMCU_DisableDBGStandbyMode	8	static
